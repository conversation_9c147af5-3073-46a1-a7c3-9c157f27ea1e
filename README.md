# CareMate API

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Express.js](https://img.shields.io/badge/Express.js-4.21+-blue.svg)](https://expressjs.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Database-blue.svg)](https://www.postgresql.org/)
[![Sequelize](https://img.shields.io/badge/Sequelize-6.37+-orange.svg)](https://sequelize.org/)
[![JWT](https://img.shields.io/badge/JWT-Authentication-red.svg)](https://jwt.io/)

CareMate API is a comprehensive healthcare management system backend that provides robust authentication, patient management, appointment scheduling, visitor management, and facility administration capabilities. The system supports both custom JWT-based authentication and Tyk API Gateway integration for enterprise-grade security and scalability.

## 🏗️ Architecture Overview

The CareMate API follows a sophisticated layered architecture pattern with clear separation of concerns and enterprise-grade features:

### 🔄 Request Flow Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Tyk Gateway   │───▶│  Express Routes  │───▶│   Validation    │
│  (Optional)     │    │                  │    │   Middleware    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Authentication │◀───│   Controllers    │◀───│  Authorization  │
│   Middleware    │    │                  │    │   Middleware    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│    Services     │◀───│  Transaction     │───▶│   Rule Engine   │
│  (Business)     │    │   Management     │    │   Triggers      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Models   │◀───│   Sequelize      │───▶│   Event Queue   │
│   + Plugins     │    │     ORM          │    │   (RabbitMQ)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │   PostgreSQL     │
                       │   Database       │
                       └──────────────────┘
```

### 🏛️ Architectural Layers

#### 1. **Presentation Layer**
- **Express.js Routes**: RESTful API endpoints with Swagger documentation
- **Middleware Stack**: Authentication, validation, rate limiting, CORS
- **Request/Response Handling**: Standardized API responses with error handling

#### 2. **Validation Layer**
- **Joi Schema Validation**: Comprehensive input validation for all endpoints
- **Custom Validators**: Master data validation, UUID validation, business rule validation
- **Error Formatting**: Structured validation error responses

#### 3. **Authentication & Authorization Layer**
- **Dual Authentication**: Custom JWT + Tyk Gateway integration
- **Multiple SSO Providers**: SAML, OpenID Connect, Azure AD
- **Role-Based Access Control**: Dynamic permissions with JWT embedding
- **Session Management**: Secure token handling with refresh mechanisms

#### 4. **Business Logic Layer**
- **Service Classes**: Encapsulated business logic and external integrations
- **Transaction Management**: Automatic database transaction handling via `catchAsync`
- **Event Processing**: Rule engine integration for business events
- **Caching Strategy**: Multi-tier caching with Redis/Memcached support

#### 5. **Data Access Layer**
- **Sequelize ORM**: Advanced PostgreSQL integration with read/write splitting
- **Model Plugins**: History tracking, pagination, media handling, rule triggers
- **Database Views**: Complex queries optimized through PostgreSQL views
- **Migration System**: Version-controlled database schema management

#### 6. **Integration Layer**
- **Message Queuing**: RabbitMQ for asynchronous processing
- **Rule Engine**: JSON-based business rules with dynamic event generation
- **External APIs**: HL7 integration, email/SMS services, file storage
- **Monitoring**: Performance tracking and audit logging

## 📁 Project Structure

```
caremate-api/
├── 📁 config/                    # Configuration files
│   ├── attributes.js             # Database attribute configurations
│   ├── caching.js               # Cache configuration
│   ├── config.js                # Main application configuration
│   ├── database.js              # Database connection setup
│   ├── logger.js                # Winston logger configuration
│   ├── morgan.js                # HTTP request logger
│   ├── passport.js              # Authentication strategies
│   ├── permissions.js           # Permission definitions
│   ├── rabbitmq.js             # Message queue configuration
│   └── sequelize.js             # Sequelize ORM setup
├── 📁 controllers/               # Request handlers
│   ├── accessLevel.controller.js
│   ├── appointment.controller.js
│   ├── auth.controller.js
│   ├── building.controller.js
│   ├── facility.controller.js
│   ├── guest.controller.js
│   ├── identity.controller.js
│   ├── patient.controller.js
│   ├── visit.controller.js
│   └── ... (20+ controllers)
├── 📁 docs/                     # API documentation
│   ├── components.yml           # Swagger components
│   └── swaggerDef.js           # Swagger configuration
├── 📁 helpers/                  # Utility functions
│   ├── agent.helper.js
│   ├── api.helper.js
│   ├── caching.helper.js
│   ├── global.helper.js
│   └── hl7.helper.js
├── 📁 middlewares/              # Express middleware
│   ├── auth.js                  # Authentication middleware
│   ├── error.js                 # Error handling
│   ├── hipaaLogger.js          # HIPAA compliance logging
│   ├── rateLimiter.js          # Rate limiting
│   ├── upload.js               # File upload handling
│   └── validate.js             # Request validation
├── 📁 migrations/               # Database migrations
├── 📁 models/                   # Sequelize models
│   ├── identity.model.js
│   ├── patient.model.js
│   ├── appointment.model.js
│   ├── facility.model.js
│   └── ... (50+ models)
├── 📁 routes/                   # API routes
│   ├── auth.route.js
│   ├── patient.route.js
│   ├── appointment.route.js
│   └── ... (25+ route files)
├── 📁 scripts/                  # Utility scripts
│   └── dbSync.js               # Database synchronization
├── 📁 seeders/                  # Database seeders
├── 📁 services/                 # Business logic
│   ├── auth.service.js
│   ├── csv.service.js
│   ├── encryption.service.js
│   ├── event.service.js
│   ├── performance.service.js
│   └── staging.service.js
├── 📁 validations/              # Request validation schemas
├── 📁 views/                    # Database views
├── 📄 app.js                    # Express application setup
├── 📄 server.js                 # Server entry point
├── 📄 package.json              # Dependencies and scripts
├── 📄 Dockerfile               # Docker configuration
└── 📄 README.md                # This file
```

## 🔧 Core Features

### 🔐 Authentication & Authorization
- **Dual Authentication System**: Custom JWT + Tyk API Gateway
- **Multi-Provider SSO**: SAML, OpenID Connect, Azure AD
- **Role-Based Access Control (RBAC)**: Dynamic permissions system
- **JWT Token Management**: Access and refresh token handling
- **Session Management**: Secure session handling with activity logging

### 🏥 Healthcare Management
- **Patient Management**: Comprehensive patient records and history
- **Appointment Scheduling**: Advanced appointment booking system
- **Visitor Management**: Guest registration and tracking
- **Facility Management**: Multi-facility support with access levels
- **Document Management**: Secure document storage and retrieval

### 🏢 Enterprise Features
- **Multi-Tenancy**: Facility-based data isolation
- **Audit Logging**: HIPAA-compliant activity tracking
- **Caching System**: Redis/Memcached for performance
- **Message Queuing**: RabbitMQ for asynchronous processing
- **Rate Limiting**: API protection and throttling

## 🛠️ Technology Stack

| Category | Technology | Version | Purpose |
|----------|------------|---------|---------|
| **Runtime** | Node.js | 18+ | JavaScript runtime |
| **Framework** | Express.js | 4.21+ | Web application framework |
| **Database** | PostgreSQL | Latest | Primary database |
| **ORM** | Sequelize | 6.37+ | Database abstraction |
| **Authentication** | JWT | 9.0+ | Token-based auth |
| **Caching** | Redis/Memcached | Latest | Performance optimization |
| **Message Queue** | RabbitMQ | Latest | Asynchronous processing |
| **Documentation** | Swagger | Latest | API documentation |
| **Security** | Helmet | 8.0+ | Security headers |
| **Validation** | Joi | 17.13+ | Request validation |
| **Logging** | Winston | 3.17+ | Application logging |
| **Testing** | Jest/Mocha | Latest | Unit and integration testing |

## 📋 Prerequisites

Before running the CareMate API, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **npm** (v8 or higher)
- **PostgreSQL** (v12 or higher)
- **Redis** (optional, for caching)
- **RabbitMQ** (optional, for message queuing)

## 🚀 Quick Start

### 1. Clone and Install
```bash
git clone https://git.onetalkhub.com/care/api.git
cd caremate-api
npm install
```

### 2. Environment Configuration
Create your environment file:
```bash
cp .env.local .env
```

### 3. Database Setup
```bash
# Sync database schema
npm run db

# Run migrations (if any)
npx sequelize-cli db:migrate

# Seed initial data
npx sequelize-cli db:seed:all
```

### 4. Start Development Server
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## 🔧 Configuration

### Environment Variables

The application uses environment-specific configuration files:

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Application environment | `local` | ✅ |
| `PORT` | Server port | `3001` | ✅ |
| `AUTH_MODE` | Authentication mode (`custom` or `tyk`) | `custom` | ✅ |
| `TYK_URL` | Tyk Gateway URL | `http://localhost:8181` | ⚠️ |
| `DB_WRITE_HOST` | Write database host | - | ✅ |
| `DB_WRITE_USERNAME` | Write database username | - | ✅ |
| `DB_WRITE_PASSWORD` | Write database password | - | ✅ |
| `DB_WRITE_DATABASE` | Write database name | - | ✅ |
| `DB_READ_HOST` | Read database host | - | ✅ |
| `DB_READ_USERNAME` | Read database username | - | ✅ |
| `DB_READ_PASSWORD` | Read database password | - | ✅ |
| `DB_READ_DATABASE` | Read database name | - | ✅ |
| `JWT_SECRET` | JWT signing secret | - | ✅ |
| `JWT_ACCESS_EXPIRATION_MINUTES` | Access token expiry | `3000` | ✅ |
| `JWT_REFRESH_EXPIRATION_DAYS` | Refresh token expiry | `30` | ✅ |
| `CACHE_DRIVER` | Cache driver (`redis`, `memcached`, `memory`) | `memory` | ❌ |
| `REDIS_HOST` | Redis host | `localhost` | ⚠️ |
| `REDIS_PORT` | Redis port | `6379` | ⚠️ |
| `RABBITMQ_URL` | RabbitMQ connection URL | - | ⚠️ |
| `EMAIL_HOST` | SMTP host | - | ⚠️ |
| `EMAIL_PORT` | SMTP port | `587` | ⚠️ |
| `EMAIL_USER` | SMTP username | - | ⚠️ |
| `EMAIL_PASS` | SMTP password | - | ⚠️ |

**Legend**: ✅ Required | ⚠️ Optional (feature-dependent) | ❌ Optional

## 🔐 Authentication Architecture

### Multiple Authentication Strategies (Passport.js)

The CareMate API implements a comprehensive authentication system using Passport.js with multiple strategies:

#### 1. **JWT Strategy (Custom Mode)**
```javascript
// JWT-based authentication for API access
passport.use("custom", new JwtStrategy({
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
}, jwtVerify));
```

**Features:**
- Bearer token authentication
- Embedded permissions in JWT payload
- Automatic token validation and user context injection
- Refresh token mechanism for extended sessions

#### 2. **SAML Strategy**
```javascript
// SAML SSO integration
passport.use("saml", new SamlStrategy({
  entryPoint: config.saml.entryPoint,
  issuer: config.saml.issuer,
  cert: config.saml.cert,
  callbackUrl: config.saml.callbackUrl
}, samlVerify));
```

**Features:**
- Enterprise SAML 2.0 SSO integration
- Identity provider federation
- Automatic user provisioning
- Attribute mapping from SAML assertions

#### 3. **OpenID Connect Strategy**
```javascript
// OpenID Connect for modern OAuth2 flows
passport.use("openidconnect", new OpenIDConnectStrategy({
  issuer: config.oidc.issuer,
  authorizationURL: config.oidc.authorizationUrl,
  tokenURL: config.oidc.tokenUrl,
  userInfoURL: config.oidc.userInfoUrl,
  clientID: config.oidc.clientId,
  clientSecret: config.oidc.clientSecret,
  callbackURL: config.oidc.callbackUrl
}, oidcVerify));
```

**Features:**
- Modern OAuth2/OpenID Connect flow
- Support for multiple identity providers
- Automatic token refresh
- User profile synchronization

#### 4. **Azure AD Strategy**
```javascript
// Microsoft Azure Active Directory integration
passport.use("azure", new AzureOIDCStrategy({
  identityMetadata: `https://login.microsoftonline.com/${config.azure.tenantId}/v2.0/.well-known/openid_configuration`,
  clientID: config.azure.clientId,
  clientSecret: config.azure.clientSecret,
  responseType: 'code',
  responseMode: 'form_post',
  redirectUrl: config.azure.callbackUrl
}, azureVerify));
```

**Features:**
- Native Azure AD integration
- Multi-tenant support
- Conditional access policy support
- Microsoft Graph API integration

### Authentication Modes

#### Custom Authentication Mode
```env
AUTH_MODE=custom
```
**Characteristics:**
- Direct JWT-based authentication
- Internal user management
- Suitable for standalone deployments
- Full control over authentication flow
- Custom login/logout endpoints

#### Tyk Gateway Mode
```env
AUTH_MODE=tyk
TYK_URL=http://localhost:8181
```
**Characteristics:**
- API Gateway handles authentication
- Header-based identity validation
- Enterprise-grade rate limiting and analytics
- Centralized authentication across microservices
- Suitable for enterprise deployments

### Dual Authentication Implementation

The system intelligently switches between authentication modes:

```javascript
const auth = (...requiredRights) => async (req, res, next) => {
  if (config.auth.mode === 'tyk') {
    // Validate Tyk headers: x-caremate-identity-id, x-caremate-permissions
    const identity = await validateTykHeaders(req, requiredRights);
    req.identity = identity;
    return next();
  } else {
    // Use Passport JWT strategy
    return passport.authenticate("custom", { session: false },
      verifyCallback(req, resolve, reject, requiredRights)
    )(req, res, next);
  }
};
```

### Permission System

**JWT Embedded Permissions:**
```json
{
  "sub": "user-uuid",
  "permissions": ["patient:read", "appointment:write", "facility:admin"],
  "type": "access",
  "iat": **********,
  "exp": **********
}
```

**Tyk Header Permissions:**
```http
x-caremate-identity-id: user-uuid
x-caremate-permissions: patient:read,appointment:write,facility:admin
x-caremate-authorized: true
```

### Database Configuration

The system supports read/write database splitting for performance:

```env
# Write Database (Master)
DB_WRITE_HOST=your-write-db-host
DB_WRITE_USERNAME=your-username
DB_WRITE_PASSWORD=your-password
DB_WRITE_DATABASE=your-database

# Read Database (Replica)
DB_READ_HOST=your-read-db-host
DB_READ_USERNAME=your-username
DB_READ_PASSWORD=your-password
DB_READ_DATABASE=your-database
```

## 📚 API Documentation

### Swagger Documentation
Access the interactive API documentation at:
- **Local**: [http://localhost:3001/docs](http://localhost:3001/docs)
- **Development**: [https://api.onetalkhub.com/docs](https://api.onetalkhub.com/docs)

### Authentication Endpoints
| Method | Endpoint | Description |
|--------|----------|-------------|
| `POST` | `/api/auth/login` | User login with email/password |
| `POST` | `/api/auth/register` | User registration |
| `POST` | `/api/auth/logout` | User logout |
| `POST` | `/api/auth/refresh-tokens` | Refresh access tokens |
| `GET` | `/api/auth/saml` | SAML SSO login |
| `GET` | `/api/auth/oidc` | OpenID Connect login |
| `GET` | `/api/auth/azure` | Azure AD login |

### Core API Endpoints
| Resource | Base Path | Description |
|----------|-----------|-------------|
| **Patients** | `/api/patients` | Patient management |
| **Appointments** | `/api/appointments` | Appointment scheduling |
| **Facilities** | `/api/facilities` | Facility management |
| **Guests** | `/api/guests` | Visitor management |
| **Identity** | `/api/identity` | User identity management |
| **Access Levels** | `/api/access-levels` | Permission management |
| **Buildings** | `/api/buildings` | Building management |
| **Rooms** | `/api/rooms` | Room management |
| **Vehicles** | `/api/vehicles` | Vehicle tracking |
| **Documents** | `/api/documents` | Document management |

## 🗄️ Database Management

### Database Synchronization
```bash
# Sync database schema (safe)
npm run db

# Sync with specific environment
npm run db -- --env-file .env.dev

# Refresh database (drops and recreates tables)
npm run db:refresh

# Refresh specific models
npm run db:refresh -- --model Facility Floor Room
```

### Database Reset Operations
```bash
# Reset default database
npm run db:reset

# Reset all databases
npm run db:reset:all

# Reset databases in parallel (fastest)
npm run db:reset:parallel

# Reset specific database
npm run db:reset:local
npm run db:reset:core
```

### Migrations and Seeders
```bash
# Run migrations
npx sequelize-cli db:migrate

# Undo last migration
npx sequelize-cli db:migrate:undo

# Seed all data
npx sequelize-cli db:seed:all

# Undo all seeders
npx sequelize-cli db:seed:undo:all

# Run specific seeder
npx sequelize-cli db:seed --seed 20230409123456-seed-users.js

# Undo specific seeder
npx sequelize-cli db:seed:undo --seed 20230409123456-seed-users.js

# With specific environment
npx sequelize-cli db:seed:all --env-file .env.dev
```

## 🚀 Deployment

### Development Environment
```bash
# Start development server with auto-reload
npm run dev

# Start with specific environment
npm start
```

### Production Deployment

#### Using PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Start application with PM2
pm2 start server.js --node-args="--env-file=.env.dev" --name="caremate_backend"

# Monitor application
pm2 status
pm2 logs caremate_backend
pm2 restart caremate_backend
```

#### Using Docker
```bash
# Build Docker image
docker build -t caremate-api .

# Run container
docker run -d \
  --name caremate-api \
  -p 3001:3001 \
  --env-file .env.dev \
  caremate-api
```

#### Environment-Specific Deployments
```bash
# Local development
npm run dev

# Development server
npm start

# Production (with PM2)
pm2 start ecosystem.config.js --env production
```

## 🔒 Security Features

### Authentication Security
- **JWT Token Validation**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Session Management**: Secure session handling
- **Multi-Factor Authentication**: Support for MFA (planned)

### API Security
- **Rate Limiting**: Configurable rate limits per endpoint
- **CORS Protection**: Cross-origin request security
- **Helmet Security**: Security headers protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Sequelize ORM protection

### HIPAA Compliance
- **Audit Logging**: Complete activity tracking
- **Data Encryption**: Sensitive data encryption
- **Access Controls**: Role-based access control
- **Secure Communication**: HTTPS enforcement

## 📊 Performance & Monitoring

### Caching Strategy
```javascript
// Cache configuration
CACHE_DRIVER=redis          // Options: redis, memcached, memory
CACHE_TTL=600              // Time-to-live in seconds

// Redis configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

### Performance Features
- **Database Read/Write Splitting**: Optimized database access
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Optimized Sequelize queries
- **Response Compression**: Gzip compression
- **Static Asset Caching**: Efficient asset delivery

### Monitoring
- **Winston Logging**: Structured application logging
- **Morgan HTTP Logging**: HTTP request logging
- **Performance Metrics**: Response time tracking
- **Error Tracking**: Comprehensive error logging

## 🛡️ Validation Layer

### Comprehensive Input Validation

Every API endpoint implements strict validation using Joi schemas with custom validators:

#### Request Validation Middleware
```javascript
const validate = (schema) => async (req, res, next) => {
  const validSchema = pick(schema, ["params", "query", "body"]);
  const object = pick(req, Object.keys(validSchema));

  try {
    const value = await Joi.compile(validSchema)
      .prefs({ errors: { label: "key" }, abortEarly: false })
      .validateAsync(object);
    Object.assign(req, value);
    return next();
  } catch (error) {
    // Format validation errors with field-specific messages
    const data = error.details.reduce((acc, curr) => {
      const field = curr.path[curr.path.length - 1];
      acc[field] = curr.message;
      return acc;
    }, {});
    return sendError(res, "Validation failed", 400, data);
  }
};
```

#### Custom Validators

**Master Data Validation:**
```javascript
const existsMasterData = (group) => {
  return catchValidationError(async (value, helpers) => {
    const existing = await MasterData.findOne({
      where: { group, key: value }
    });
    if (!existing) {
      return helpers.message(
        `MasterData entry not found for group "${group}" with key "${value}"`
      );
    }
    return value;
  });
};

// Usage in validation schema
const facilityValidation = {
  body: Joi.object({
    status: Joi.string().custom(existsMasterData('facility_status')),
    type: Joi.string().custom(existsMasterData('facility_type'))
  })
};
```

## ⚡ Transaction Management with catchAsync

### Automatic Transaction Handling

The `catchAsync` helper eliminates the need for manual try-catch blocks and provides automatic transaction management:

```javascript
const catchAsync = (fn, options = {}) => {
  const { useTransaction = true } = options;
  return async (req, res, next) => {
    if (!useTransaction) {
      return Promise.resolve(fn(req, res, next)).catch(next);
    }

    // Automatic transaction creation
    const transaction = await sequelize.transaction();
    req.transaction = transaction;

    // Automatic trace context for audit logging
    if (["POST", "PUT", "PATCH", "DELETE"].includes(req.method)) {
      req.traceContext = await createTraceContextByReq(req);
    }

    try {
      // Inject updated_by from authenticated user
      if (req.identity && req.body) {
        req.body.updated_by = req.identity.identity_id;
      }

      await fn(req, res, next);
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      next(error);
    }
  };
};
```

### Controller Implementation
```javascript
// Clean controller code without try-catch
const createPatient = catchAsync(async (req, res) => {
  const patient = await Patient.create(req.body, {
    transaction: req.transaction
  });
  sendSuccess(res, 'Patient created successfully', 201, patient);
});

// Validation + Transaction + Business Logic
router.post('/patients',
  validate(patientValidation.create),
  auth('patient:create'),
  createPatient
);
```

## 🔌 Model Plugins System

### 1. History Plugin - Complete Audit Trail

Automatically tracks all changes to model data with detailed history:

```javascript
// Apply history plugin to model
const historyPlugin = require('../plugins/history.plugin');
historyPlugin(Patient, sequelize, DataTypes, ['sensitive_field']);

// Creates two additional tables:
// - patient_transaction: Records operations (create/update/delete)
// - patient_history: Records field-level changes
```

**Generated Tables:**
```sql
-- Transaction table
CREATE TABLE patient_transaction (
  patient_transaction_id UUID PRIMARY KEY,
  patient_id UUID NOT NULL,
  operation ENUM('0', '1', '2'), -- 0:Create, 1:Update, 2:Delete
  updated_by UUID,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

-- History table
CREATE TABLE patient_history (
  patient_history_id UUID PRIMARY KEY,
  patient_transaction_id UUID NOT NULL,
  column_name VARCHAR NOT NULL,
  old_value TEXT,
  new_value TEXT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### 2. Pagination Plugin - Efficient Data Retrieval

Standardized pagination for all models and arrays:

```javascript
const { paginate } = require('../plugins/paginate.plugin');

// Paginate Sequelize models
const patients = await paginate(Patient,
  { where: { status: 'active' } },
  { page: 1, limit: 10, sortBy: 'created_at', sortOrder: 'DESC' }
);

// Paginate arrays
const paginatedArray = await paginate(dataArray, {}, { page: 2, limit: 5 });

// Response format
{
  totalItems: 150,
  totalPages: 15,
  currentPage: 1,
  data: [...] // Actual records
}
```

### 3. Trigger Plugin - Rule Engine Integration

Automatically triggers business rules on model operations:

```javascript
// Apply trigger plugin with rules
const triggerPlugin = require('../plugins/trigger.plugin');
triggerPlugin(Patient, ['create', 'update'], [
  {
    name: 'patient_created_notification',
    conditions: {
      all: [
        { fact: 'status', operator: 'equal', value: 'active' },
        { fact: 'email', operator: 'notEqual', value: null }
      ]
    },
    event: {
      type: 'patient_notification',
      params: { action: 'welcome_email' }
    }
  }
]);
```

**Rule Engine Features:**
- JSON-based rule definitions
- Dynamic fact evaluation
- Multiple event triggers per rule
- Bulk operation optimization
- Performance monitoring integration

### 4. Media Plugin - Image Storage & Processing

Handles file uploads with automatic thumbnail generation:

```javascript
// Define media fields in model
const Patient = sequelize.define('Patient', {
  image: {
    type: MEDIA, // Custom media type
    allowNull: true
  },
  documents: {
    type: MEDIA,
    allowMultiple: true
  }
});

// Apply media plugin
const mediaPlugin = require('../plugins/media.plugin');
mediaPlugin(Patient, sequelize, DataTypes);
```

**Media Features:**
- Base64 image processing with Sharp
- Automatic thumbnail generation (200px width, AVIF format)
- Separate media storage table
- Image format detection and validation
- Optimized storage with foreign key relationships

## 🎯 Rule Engine System

### JSON Rules Engine Integration

The CareMate API implements a sophisticated rule engine using `json-rules-engine` for dynamic business logic:

#### Rule Definition Structure
```javascript
const patientRules = [
  {
    name: 'high_priority_patient',
    conditions: {
      all: [
        { fact: 'age', operator: 'greaterThan', value: 65 },
        { fact: 'condition', operator: 'in', value: ['critical', 'urgent'] }
      ]
    },
    event: [
      { type: 'priority_alert', params: { level: 'high' } },
      { type: 'notification', params: { recipients: ['doctor', 'nurse'] } }
    ]
  }
];
```

#### Event Processing Flow
```
Model Operation → Trigger Plugin → Rules Engine → Event Generation → RabbitMQ → External Processing
```

#### Rule Engine Features
- **Dynamic Rule Loading**: Rules loaded from database configuration
- **Fact Validation**: Automatic validation against model schema
- **Bulk Processing**: Optimized for bulk operations with dependency analysis
- **Performance Monitoring**: Built-in performance tracking
- **Event Prioritization**: Configurable event ordering and priority

#### Event Generation
```javascript
// Automatic event creation and queuing
const processEvents = async (events, instance, traceId) => {
  for (const event of events.sort((a, b) => b.order - a.order)) {
    const eventPayload = {
      event_id: uuidv4(),
      type: event.type,
      data: instance.get({ plain: true }),
      params: event.params,
      queue: event.queue || 'default',
      order: event.order || 0,
      trace_id: traceId
    };

    await sendToRabbitMQ(eventPayload);
  }
};
```

## 📊 Master Data Concept

### Centralized Reference Data Management

Master Data provides a flexible system for managing reference values across the application:

#### Master Data Structure
```sql
CREATE TABLE master_data (
  master_data_id UUID PRIMARY KEY,
  group VARCHAR NOT NULL,        -- Category (e.g., 'facility_status')
  key VARCHAR NOT NULL,          -- Code (e.g., 'active')
  value VARCHAR NOT NULL,        -- Display value (e.g., 'Active')
  description TEXT,
  sort_order INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

#### Usage in Models
```javascript
// Instead of hardcoded enums, use master data references
const Facility = sequelize.define('Facility', {
  status: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isValidMasterData: existsMasterData('facility_status')
    }
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      isValidMasterData: existsMasterData('facility_type')
    }
  }
});
```

#### Cached Master Data Retrieval
```javascript
// Efficient cached retrieval
const getMasterData = catchAsync(async (req, res) => {
  const { groups } = req.query; // ['facility_status', 'facility_type']
  const result = {};

  await Promise.all(groups.map(async (group) => {
    const cacheKey = `masterData:${group}`;
    let cachedData = await cache.get(cacheKey);

    if (!cachedData) {
      const data = await MasterData.findAll({
        where: { group, is_active: true },
        attributes: ['key', 'value'],
        order: [['sort_order', 'ASC']]
      });
      cachedData = data.map(item => ({
        key: item.key,
        value: item.value
      }));
      await cache.set(cacheKey, cachedData, 3600); // 1 hour TTL
    }

    result[group] = cachedData;
  }));

  sendSuccess(res, 'Master data retrieved', 200, result);
});
```

#### Benefits
- **Flexibility**: Easy to add new reference values without code changes
- **Consistency**: Centralized management of lookup values
- **Performance**: Cached retrieval with automatic invalidation
- **Internationalization**: Support for multiple languages
- **Audit Trail**: Track changes to reference data

## 🗄️ Database Views & Migrations

### Complex Query Optimization

The system uses PostgreSQL views for complex, frequently-used queries:

#### View Migration Example
```javascript
// migrations/create-view-patient-appointment.js
module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(`
      CREATE VIEW view_patient_appointment AS
      SELECT
        a.appointment_id,
        a.patient_id,
        a.appointment_date,
        (p.first_name || ' ' || p.last_name) AS patient_name,
        ms_status.value AS appointment_status_name,
        ms_type.value AS appointment_type_name,
        f.name AS facility_name,
        pi.identifier_value AS mrn
      FROM appointment a
      LEFT JOIN patient p ON a.patient_id = p.patient_id
      LEFT JOIN facility f ON a.facility_id = f.facility_id
      LEFT JOIN patient_identifier pi ON pi.patient_id = p.patient_id
      LEFT JOIN master_data ms_status
        ON ms_status.key = a.status
        AND ms_status.group = 'appointment_status'
      LEFT JOIN master_data ms_type
        ON ms_type.key = a.type
        AND ms_type.group = 'appointment_type';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.query(
      `DROP VIEW IF EXISTS view_patient_appointment;`
    );
  }
};
```

#### View Model Definition
```javascript
// views/patientAppointment.view.js
module.exports = (sequelize, DataTypes) => {
  const PatientAppointmentView = sequelize.define('PatientAppointmentView', {
    appointment_id: { type: DataTypes.UUID, primaryKey: true },
    patient_id: DataTypes.UUID,
    appointment_date: DataTypes.DATE,
    patient_name: DataTypes.STRING,
    appointment_status_name: DataTypes.STRING,
    appointment_type_name: DataTypes.STRING,
    facility_name: DataTypes.STRING,
    mrn: DataTypes.STRING
  }, {
    tableName: 'view_patient_appointment',
    timestamps: false
  });

  return PatientAppointmentView;
};
```

#### Benefits of Database Views
- **Performance**: Pre-optimized complex joins
- **Consistency**: Standardized data presentation
- **Security**: Controlled data access
- **Maintainability**: Centralized query logic
- **Reusability**: Shared across multiple endpoints

## 🔧 Development Guidelines

### Code Style & Standards
- **ESLint**: JavaScript linting with healthcare-specific rules
- **Prettier**: Consistent code formatting
- **Naming Conventions**: Clear, descriptive naming patterns
- **File Organization**: Modular, feature-based structure

### API Development Pattern
```javascript
// Standard controller pattern
const { catchAsync, sendSuccess } = require('../helpers/api.helper');
const { paginate } = require('../models/plugins/paginate.plugin');

const getPatients = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, search } = req.query;

  const where = search ? {
    [Op.or]: [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } }
    ]
  } : {};

  const patients = await paginate(Patient, { where }, { page, limit });
  sendSuccess(res, 'Patients retrieved successfully', 200, patients);
});

// Route definition with full middleware stack
router.get('/patients',
  validate(patientValidation.getPatients),
  auth('patient:read'),
  getPatients
);
```

### Database Modeling Best Practices
```javascript
// Enhanced model with plugins
module.exports = (sequelize, DataTypes) => {
  const Patient = sequelize.define('Patient', {
    patient_id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    first_name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: { len: [1, 100] }
    },
    image: {
      type: MEDIA,
      allowNull: true
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isValidMasterData: existsMasterData('patient_status')
      }
    }
  });

  // Apply plugins
  const historyPlugin = require('../plugins/history.plugin');
  const triggerPlugin = require('../plugins/trigger.plugin');
  const mediaPlugin = require('../plugins/media.plugin');

  historyPlugin(Patient, sequelize, DataTypes);
  triggerPlugin(Patient, ['create', 'update'], patientRules);
  mediaPlugin(Patient, sequelize, DataTypes);

  Patient.associate = (models) => {
    Patient.hasMany(models.Appointment);
    Patient.belongsTo(models.Facility);
  };

  return Patient;
};
```

## 📄 License

This project is proprietary software. All rights reserved.

---

**CareMate API** - Healthcare Management System Backend
*Built with ❤️ by the OneTalkHub Team*